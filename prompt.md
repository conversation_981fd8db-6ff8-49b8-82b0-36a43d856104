写代码的时候，遵循下面的逻辑：
  . 在写代码之前，充分梳理这次需求所涉及的上下文
  . 设计代码逻辑的时候，单个文件不要超过200行，如果超过了，就封装到单独的文件中
  . 设计代码逻辑的时候，必要的时候进行适当的封装函数，确保代码逻辑清晰易读
  . 中文回答，写完后尝试编译
  . 除了我当前提到的业务和相关逻辑，不要修改任何其他不相关的代码
  . 写完代码尝试编译测试环境，确保编译通过。编译的方式是用/Users/<USER>/fang/flutter/bin/flutter build apk --debug，记得是打测试包不是生产包
  . 写代码的过程中，自行判断是否要新增测试文件，如果新增了测试文件，测试完成之后要删除
  . 写完代码之后，检查一下在这次实现的过程中新增的代码，有没有哪些是可以删除的，不要增加无用的代码

我的需求是：
<html lang="zh"><head></head><body>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>理财</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
    }
    .header {
      background-color: #ffffff;
      padding: 40px 20px 20px;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
这是原始的笔记数据，但是我修改了一下草稿，然后撤销，理论上应该跟上面保持一致，但是会变成下面的数据：

"<html lang="zh"><head></head><body>"
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>理财</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
    }
    .header {
      background-color: #ffffff;
      padding: 40px 20px 20px;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 16px;
    }
    .table-of-contents {
      background: #fff;
      margin-bottom: 24px;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .toc-title {
      font-size: 18px;
      font-weight: 600;
      color: #222;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    .toc-title::before {
      content: "📋";
      margin-right: 8px;
    }
    .toc-item {
      padding: 10px 0;
      font-size: 14px;
      color: #2d72ff;
      cursor: pointer;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.2s ease;
      position: relative;
    }
    .toc-item:last-child {
      border-bottom: none;
    }
    .toc-item:hover {
      color: #1a5ce6;
      background-color: #f8f9ff;
      padding-left: 8px;
    }
    .toc-item.level-1 {
      font-weight: 500;
      padding-left: 0;
    }
    .toc-item.level-2 {
      padding-left: 20px;
      font-size: 13px;
      color: #5a6c7d;
    }
    .toc-item.level-2:hover {
      color: #2d72ff;
      padding-left: 28px;
    }
    .toc-item.level-3 {
      padding-left: 40px;
      font-size: 12px;
      color: #8a9ba8;
    }
    .toc-item.level-3:hover {
      color: #2d72ff;
      padding-left: 48px;
    }
    .content-area {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .card {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px solid #f0f2f5;
    }
    .card:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .card h1, .card h2, .card h3, .card h4 {
      margin: 0 0 12px;
      color: #222;
      scroll-margin-top: 80px;
      line-height: 1.4;
    }
    .card h1 {
      font-size: 18px;
      font-weight: 700;
      border-bottom: 2px solid #2d72ff;
      padding-bottom: 6px;
    }
    .card h2 {
      font-size: 16px;
      font-weight: 600;
    }
    .card h3 {
      font-size: 15px;
      font-weight: 600;
    }
    .card h4 {
      font-size: 14px;
      font-weight: 500;
    }
    .card p {
      margin: 0 0 14px;
      color: #444;
      font-size: 14px;
      line-height: 1.6;
    }
    .card p:last-child {
      margin-bottom: 0;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      .container {
        padding: 12px;
      }
      .header {
        padding: 30px 16px 16px;
        font-size: 20px;
      }
      .table-of-contents {
        padding: 16px;
        margin-bottom: 16px;
      }
      .toc-title {
        font-size: 16px;
        margin-bottom: 12px;
      }
      .content-area {
        padding: 16px;
      }
      .card {
        margin-bottom: 20px;
        padding-bottom: 16px;
      }
      .card h1 {
        font-size: 16px;
        padding-bottom: 4px;
      }
      .card h2 {
        font-size: 15px;
      }
      .card h3 {
        font-size: 14px;
      }
      .card h4 {
        font-size: 13px;
      }
      .card p {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 12px;
      }
    }
  </style>


  <div class="header" contenteditable="false">理财</div>

  <div class="container" contenteditable="false">
    <div class="table-of-contents" id="tableOfContents" contenteditable="false">
      <div class="toc-title" contenteditable="false">目录</div>
      
      
      
      <div class="toc-item level-1" data-target="section-2" contenteditable="false" style="">核心原则</div><div class="toc-core-subgroup" style="padding-left: 20px; border-left: 2px solid rgb(45, 114, 255);" contenteditable="false"><div class="toc-item level-2" data-target="section-2-1" contenteditable="false" style="">收入分配原则</div><div class="toc-item level-2" data-target="section-2-2" contenteditable="false" style="">流动资金配置</div><div class="toc-item level-2" data-target="section-2-3" contenteditable="false" style="">高收益投资策略</div></div><div class="toc-item level-1" data-target="section-2-new-method" contenteditable="false" style="">具体理财方法</div><div class="toc-method-subgroup" style="padding-left: 20px; border-left: 2px solid rgb(45, 114, 255);" contenteditable="false"><div class="toc-item level-2" data-target="section-2-4" contenteditable="false" style="">稳健投资原则</div><div class="toc-item level-2" data-target="section-2-5" contenteditable="false" style="">储蓄与资产配置建议</div><div class="toc-item level-2" data-target="section-2-6" contenteditable="false" style="">银行理财信息差及高收益存款策略</div></div>
      
      
      
      <div class="toc-item level-3" data-target="section-2-6-1" contenteditable="false">短期活钱理财收益提升</div><div class="toc-item level-3" data-target="section-2-6-2" contenteditable="false">中期特色存款利率优势</div><div class="toc-item level-3" data-target="section-2-6-3" contenteditable="false">长期资产稳健增值策略</div>
      <div class="toc-item level-3" data-target="section-2-4-1" contenteditable="false" style="">短期闲钱配置</div>
      <div class="toc-item level-3" data-target="section-2-4-2" contenteditable="false" style="">小金库的优势</div>
      
      
      
      
      
    </div>

    <div class="content-area" contenteditable="false">
      

      

      

      <div class="card" id="section-2" contenteditable="false" style="transition: background-color 0.3s;">
        <h1 contenteditable="false">核心原则</h1>
        <p contenteditable="false">📊 理财的核心在于科学分配和稳健增值，避免盲目投资与过度消费。</p>
      </div>

      

      

      

      <div id="core-principle-group" contenteditable="false" style="padding-left: 20px; border-left: 2px solid rgb(45, 114, 255); margin-top: 10px;"><div class="card" id="section-2-1" contenteditable="false" style="transition: background-color 0.3s;">
        <h2 contenteditable="false">收入分配原则</h2>
        <p contenteditable="false">💰 收入划分为稳健投资60%，搏取高收益30%，流动资金10%，合理分配助财富稳步累积和风险控制。</p>
      </div><div class="card" id="section-2-2" contenteditable="false">
        <h2 contenteditable="false">流动资金配置</h2>
        <p contenteditable="false">🏦 10%的流动资金用于满足生活开支，推荐放入货币基金（如余额宝）或银行，兼顾灵活性与安全性，同时获得约1.5%的收益。</p>
      </div><div class="card" id="section-2-3" contenteditable="false" style="transition: background-color 0.3s;">
        <h2 contenteditable="false">高收益投资策略</h2>
        <p contenteditable="false">📈 30%资金用于相对稳定的高收益投资，如支付宝债基，可获得约6%收益，风险低且收益稳健。</p>
      </div></div><div class="card" id="section-2-new-method" contenteditable="false" style="transition: background-color 0.3s;"><h2 contenteditable="false">具体理财方法</h2><p contenteditable="false">这里可以填写具体的方法细节哦～</p></div><div id="new-method-group" contenteditable="false" style="padding-left: 20px; border-left: 2px solid rgb(45, 114, 255); margin-top: 10px;"><div class="card" id="section-2-4" contenteditable="false" style="transition: background-color 0.3s;">
        <h2 contenteditable="false">稳健投资原则</h2>
        <p contenteditable="false">🛡️ 60%用于稳健投资，遵循“鸡蛋不放一篮子”的原则，分散配置以降低风险。</p>
      </div><div class="card" id="section-2-5" contenteditable="false" style="transition: background-color 0.3s;"><h2 contenteditable="false">储蓄与资产配置建议</h2><p contenteditable="false">💡 建议一：重视储蓄并量化其金额，储蓄是自由和安全的基础。建议二：避免只持有现金，因其长期收益低于通胀，应多元化投资不同资产和国家以分散风险。建议三：投资时逆势操作，抛开直觉，逢低买入逢高卖出，才能获得更好长期回报。</p></div><div class="card" id="section-2-6" contenteditable="false"><h2 contenteditable="false">银行理财信息差及高收益存款策略</h2><p contenteditable="false">💡 掌握银行活钱管理、特色定存和长期资产增值中的利率信息差，能显著提高收益率。</p></div></div>

      <div class="card" id="section-2-4-1" contenteditable="false" style="transition: background-color 0.3s;">
        <h3 contenteditable="false">短期闲钱配置</h3>
        <p contenteditable="false">📅 短期闲钱投资国债或银行大额存单，利率竞争优势明显，尤其年终奖等资金优先考虑此类配置。</p>
      </div>

      <div class="card" id="section-2-4-2" contenteditable="false" style="transition: background-color 0.3s;">
        <h3 contenteditable="false">小金库的优势</h3>
        <p contenteditable="false">🏦 小金库是安全稳定的投资选项，支持灵活提取与长期增值，兼顾安全与收益。</p>
      </div><div class="card" id="section-2-6-1" contenteditable="false"><h3 contenteditable="false">短期活钱理财收益提升</h3><p contenteditable="false">当前活期收益低至0.15%，余额宝约1.5%。部分银行货币基金产品年化收益达2.19%-2.8%，如工商银行的天天银和众邦银行的帮钱包。20万存半载，收益可翻倍。风险与余额宝相似，但收益有显著差距。</p></div><div class="card" id="section-2-6-2" contenteditable="false"><h3 contenteditable="false">中期特色存款利率优势</h3><p contenteditable="false">3-5年期定存主流，特色存款利率高于大银行，如华夏银行年年乐3年利率2.4%，广东华兴银行5年2.6%。部分地方银行如贵安发展银行5年期高达3.6%，20万存5年比大型银行多赚2万元，50万以内本金受存款保险保障。</p></div><div class="card" id="section-2-6-3" contenteditable="false"><h3 contenteditable="false">长期资产稳健增值策略</h3><p contenteditable="false">5年以上长期资金，养老金和教育金需稳健增值。超长国债难买，基金债券波动大。核心资产锁定高利率复利，合同保障本金和收益，中途取用剩余资金继续复利增长。</p><p contenteditable="false">例：20万元投入，20年增至超30万，30年近40万，40年约50万，长期年均收益超过6%。懂得此法者资产稳健翻倍，收益稳定不受降息影响。</p></div>

      

      

      

      

      
    </div>
  </div>
  <script data-id="toc-navigation">
    // 目录点击跳转功能
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('toc-item')) {
        var targetId = e.target.getAttribute('data-target');
        var targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          targetElement.style.backgroundColor = '#f8f9ff';
          targetElement.style.transition = 'background-color 0.3s ease';
          setTimeout(function() {
            targetElement.style.backgroundColor = '';
          }, 2000);
        }
      }
    });

    // 滚动时高亮当前章节对应的目录项
    function highlightCurrentSection() {
      var sections = document.querySelectorAll('.card[id^="section-"]');
      var tocItems = document.querySelectorAll('.toc-item');
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      var currentSection = null;

      sections.forEach(function(section) {
        var sectionTop = section.offsetTop - 100;
        if (scrollTop >= sectionTop) {
          currentSection = section;
        }
      });

      tocItems.forEach(function(item) {
        item.style.fontWeight = '';
        item.style.color = '';
      });

      if (currentSection) {
        var currentId = currentSection.getAttribute('id');
        var currentTocItem = document.querySelector('[data-target="' + currentId + '"]');
        if (currentTocItem) {
          currentTocItem.style.fontWeight = '600';
          currentTocItem.style.color = '#1a5ce6';
        }
      }
    }

    var ticking = false;
    window.addEventListener('scroll', function() {
      if (!ticking) {
        requestAnimationFrame(function() {
          highlightCurrentSection();
          ticking = false;
        });
        ticking = true;
      }
    });

    document.addEventListener('DOMContentLoaded', highlightCurrentSection);
  </script>

<script>
    // 移除理财故事和案例分析相关部分
    const removeIds = [
      'section-1', 'section-1-1', 'section-1-2',
      'section-3', 'section-3-1', 'section-3-2', 'section-3-3'
    ];
    removeIds.forEach(id => {
      const elem = document.getElementById(id);
      if (elem) elem.remove();
    });

    // 移除目录中对应的目录项
    const tocRemoveTexts = [
      '1. 理财故事启示','1.1 林依晨的理财智慧','1.2 理财法升级与成果',
      '3. 小金库案例分析','3.1 理财效果示例','3.2 长期收益与风险','3.3 使用建议'
    ];
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      if (tocRemoveTexts.includes(item.textContent.trim())) {
        item.remove();
      }
    });
  </script><script>
    // 更新目录所有剩余项文本内容，去除开头的数字和点
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      item.textContent = item.textContent.replace(/^d+(.d+)*s*/, '');
    });
  </script><script>
    // 由于之前内容是contenteditable，先清空再commit内容避免有残留样式
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      // 仅修改纯文本内容，确保编辑权限一致
      const text = item.textContent.replace(/^d+(.d+)*s*/, '');
      item.textContent = text;
    });
  </script><script>
    // 这个正则在之前的操作中有误，重新书写为去除数字+点+空白符
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      item.textContent = item.textContent.replace(/^d+(.d+)*s*/, '');
    });
  </script><script>
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      // 去除2.开头的数字和点以及空格
      item.textContent = item.textContent.replace(/^d+.s*/, '');
    });
  </script><script>
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      // 统一去除所有开头的序号格式，如2.2 、2.3 、2.4.1等
      item.textContent = item.textContent.replace(/^d+(.d+)*.s*/, '');
    });
  </script><script>
    // 这次保证彻底去掉所有首部数字和点还有空格，包括可能的2.2和2.2.1格式
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      item.textContent = item.textContent.replace(/^d+(.d+)*s*/, '');
    });
  </script><script>
    // 删除结语与联系这一部分
    const section = document.getElementById('section-4');
    if (section) {
      section.remove();
    }
    
    // 同时移除目录对应项
    const tocItems = document.querySelectorAll('.toc-item');
    tocItems.forEach(item => {
      if (item.getAttribute('data-target') === 'section-4') {
        item.remove();
      }
    });
  </script><script>
    // 确保完整删除“结语与联系”部分
    const section = document.getElementById('section-4');
    if (section) {
      section.remove();
    }

    // 删除目录对应项
    const tocItems = Array.from(document.querySelectorAll('.toc-item'));
    tocItems.forEach(item => {
      if (item.getAttribute('data-target') === 'section-4') {
        item.remove();
      }
    });

    // 额外检查，避免多个副本存在，循环删除
    while(document.querySelector('#section-4')) {
      document.querySelector('#section-4').remove();
    }
    
    while(document.querySelector('.toc-item[data-target="section-4"]')) {
      document.querySelector('.toc-item[data-target="section-4"]').remove();
    }
  </script><script>
    // 再次强力删除“结语与联系”区块
    const removeSection = () => {
      const section = document.getElementById('section-4');
      if (section) {
        section.remove();
        return true;
      }
      return false;
    };

    // 删除目录项
    const removeToc = () => {
      const toc = document.querySelectorAll('.toc-item');
      toc.forEach(item => {
        if (item.getAttribute('data-target') === 'section-4') {
          item.remove();
        }
      });
    };

    // 删除执行循环，直到该部分完全消失
    while(removeSection()){}
    removeToc();
  </script><script>
    // 精炼文本内容，去除重复和冗余
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
      const p = card.querySelector('p');
      if (p) {
        let text = p.textContent.trim();
        // 去除关于“风险低且收益稳定”描述的重复
        if (/风险低且收益稳定/.test(text)) {
          // 合并两个小金库优势和稳健投资原则里类似描述
          if (card.id === 'section-2-4-2') {
            p.textContent = '🏦 小金库是安全稳定的投资选项，支持灵活提取与长期增值，兼顾安全与收益。';
          }
          if (card.id === 'section-2-4') {
            p.textContent = '🛡️ 60%用于稳健投资，遵循分散配置原则以降低风险。';
          }
        }
        // 减少重复性描述
        if (card.id === 'section-2-1') {
          p.textContent = '💰 收入划分为稳健投资60%，搏取高收益30%，流动资金10%，合理分配助财富稳步累积和风险控制。';
        }
      }
    });
  </script>"""</body></html>"

  开头和结尾有莫名其妙的双引号